<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<div class="toolbar-sorter sorter">
    <select data-role="sorter"
            class="form-select sorter-options mr-3 w-full md:w-auto"
            aria-label="<?= $escaper->escapeHtml(__('Sort By')) ?>"
            @change="changeUrl(
                'product_list_order',
                $event.currentTarget.options[$event.currentTarget.selectedIndex].value,
                options.orderDefault
            )">
        <?php foreach ($block->getAvailableOrders() as $orderCode => $orderLabel):?>
            <option value="<?= $escaper->escapeHtmlAttr($orderCode) ?>"
                <?php if ($block->isOrderCurrent($orderCode)): ?>
                    selected="selected"
                <?php endif; ?>
                >
                <?= $escaper->escapeHtml(__($orderLabel)) ?>
            </option>
        <?php endforeach; ?>
    </select>
    <?php if ($block->getCurrentDirection() == 'desc'): ?>
        <a title="<?= $escaper->escapeHtmlAttr(__('Set Ascending Direction')) ?>"
           href="#"
           class="action sorter-action sort-desc"
           @click.prevent="changeUrl('product_list_dir', 'asc', options.directionDefault)"
        >
        <i class="fa-solid fa-arrow-up text-2xl text-cblue"></i>
            <!-- <?= $heroicons->sortAscendingHtml(); ?> -->
        </a>
    <?php else: ?>
        <a title="<?= $escaper->escapeHtmlAttr(__('Set Descending Direction')) ?>"
           href="#"
           class="action sorter-action sort-asc"
           @click.prevent="changeUrl('product_list_dir', 'desc', options.directionDefault)"
        >
        <i class="fa-solid fa-arrow-down text-2xl text-cblue"></i>
        <!-- <?= $heroicons->sortDescendingHtml(); ?> -->
        </a>
    <?php endif; ?>
</div>
