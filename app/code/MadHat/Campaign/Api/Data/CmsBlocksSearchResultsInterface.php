<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\Campaign\Api\Data;

interface CmsBlocksSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Cmsblocks list.
     * @return \MadHat\Campaign\Api\Data\CmsBlocksInterface[]
     */
    public function getItems();

    /**
     * Set block_id list.
     * @param \MadHat\Campaign\Api\Data\CmsBlocksInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

