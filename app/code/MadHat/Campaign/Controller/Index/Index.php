<?php
namespace MadHat\Campaign\Controller\Index;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Framework\Controller\Result\RedirectFactory;

class Index implements HttpGetActionInterface
{
    private $session;
    private $request;
    private $messageManager;
    private $resultRedirectFactory;

    public function __construct(
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        \Magento\Framework\App\Cache\Frontend\Pool $cacheFrontendPool,
        RequestInterface $request,
        CustomerSession $session,
        ManagerInterface $messageManager,
        RedirectFactory $resultRedirectFactory
    ) {
        $this->cacheTypeList = $cacheTypeList;
        $this->cacheFrontendPool = $cacheFrontendPool;
        $this->request = $request;
        $this->session = $session;
        $this->messageManager = $messageManager;
        $this->resultRedirectFactory = $resultRedirectFactory;
    }

    /**
     * Add CampaignParams from url to session
     * Example URL https://magento2.docker/campaigns/?campaignID=1&campaignParam1=param1&campaignParam2=param2
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $params = $this->request->getParams();
        if (isset($params['campaignID'])) {
            $this->session->setCampaignParams($params);
            $this->messageManager->addSuccessMessage(__('Campaign parameters saved successfully.'));
        } else {
            $this->messageManager->addErrorMessage(__('Missing campaignID.'));
        }

//        $types = ['config','layout','block_html','collections','reflection','db_ddl','eav','config_integration','config_integration_api','full_page','translate','config_webservice'];
//        $types = ['full_page'];
//        foreach ($types as $type) {
//            $this->cacheTypeList->cleanType($type);
//        }
//        foreach ($this->cacheFrontendPool as $cacheFrontend) {
//            $cacheFrontend->getBackend()->clean();
//        }

        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setPath('/');
        return $resultRedirect;
    }
}
