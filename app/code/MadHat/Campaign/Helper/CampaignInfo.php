<?php

namespace MadHat\Campaign\Helper;

use MadHat\Campaign\Logger\Logger;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\UrlInterface;
use MadHat\Campaign\Model\Session;
use MadHat\Campaign\Model\ResourceModel\Campaign\CollectionFactory;
use Magento\Framework\App\Helper\AbstractHelper;

class CampaignInfo extends AbstractHelper
{
    /*** @var ProductRepositoryInterface */
    protected $productRepository;

    /*** @var UrlInterface */
    protected $urlInterface;

    /** @var Config */
    protected $config;

    /** @var CollectionFactory */
    protected $campaignCollection;

    /** @var Session */
    protected $campaignSession;

    /** @var Logger */
    protected $campaignLogger;

    /**
     * Constructor
     * @param Context $context
     * @param ProductRepositoryInterface $productRepository
     * @param UrlInterface $urlInterface ,
     * @param Config $config
     * @param CollectionFactory $campaignCollection
     * @param Session $campaignSession
     * @param Logger $campaignLogger
     */
    public function __construct(
        Context $context,
        ProductRepositoryInterface $productRepository,
        UrlInterface $urlInterface,
        Config $config,
        CollectionFactory $campaignCollection,
        Session $campaignSession,
        Logger $campaignLogger
    ) {
        $this->productRepository = $productRepository;
        $this->urlInterface = $urlInterface;
        $this->config = $config;
        $this->campaignCollection = $campaignCollection;
        $this->campaignSession = $campaignSession;
        $this->campaignLogger = $campaignLogger;
        parent::__construct($context);
    }

    public function getRedirectUrl($requestParams, $campaignAdid)
    {
        $urlKey = '';
//        if (isset($requestParams[$this->config->getAddToCartParam()])) {
//            $urlKey = 'checkout/cart/index/';
//        } else
        if (isset($requestParams[$this->config->getProdParam()]) && $requestParams[$this->config->getProdParam()] != '') {
            $sku = $requestParams[$this->config->getProdParam()];
            $urlKey = $this->getProductUrlKeyBySKU($sku);
        } elseif (isset($requestParams[$this->config->getPageParam()]) && $requestParams[$this->config->getPageParam()]) {
            $urlKey = $requestParams[$this->config->getPageParam()];
        } elseif (isset($requestParams[$this->config->getBrandParam()]) && $requestParams[$this->config->getBrandParam()]) {
            $urlKey = 'brands/' . $requestParams[$this->config->getBrandParam()];
        } elseif (isset($requestParams[$this->config->getTypeParam()]) && $requestParams[$this->config->getTypeParam()]) {
            $urlKey = 'snus/' . $requestParams[$this->config->getTypeParam()];
        } elseif (isset($requestParams[$this->config->getSearchParam()]) && $requestParams[$this->config->getSearchParam()]) {
            $urlKey = "catalogsearch/result/";
        } elseif ($this->getLandingPageFromCampaign($campaignAdid)) {
            $urlKey = $this->getLandingPageFromCampaign($campaignAdid);
        } else {
            $urlKey = '';
        }

        $queryParams = $this->getCampaignParamsArray($campaignAdid);
        if ($queryParams && is_array($queryParams)) {
            $query = '';
            if ($urlKey == "catalogsearch/result/") {
                $query = $query . '?q=' . $requestParams[$this->config->getSearchParam()] . '&';
            } else {
                $query .= '?';
            }
            if ($queryParams !== null) {
                $query .= http_build_query($queryParams, '', '&', PHP_QUERY_RFC3986);
            }
            if ($urlKey != '') {
                $newUrl = $this->urlInterface->getBaseUrl() . $urlKey . $query;
            } else {
                $newUrl = $this->urlInterface->getBaseUrl() . $query;
            }
            return $newUrl;
        }
        return false;
    }

    /**
     * This method returns the url basedon product sku
     * @return string|null
     */
    public function getProductUrlKeyBySKU($sku): ?string
    {
        try {
            $urlKey = $this->productRepository->get($sku)->getProductUrl();
            if ($urlKey != '' && $urlKey != null) {
                return str_replace($this->urlInterface->getBaseUrl(), "", $urlKey);
            }
        } catch (NoSuchEntityException $noSuchEntityException) {
            $this->campaignLogger->error('SKU not exist for URL : ' . $sku);
        }
        return false;
    }

    public function getLandingPageFromCampaign($campaignAdid)
    {
        $collection = $this->campaignCollection->create();
        $collection->addFieldToSelect('landingpage')->addFieldToFilter('adid', $campaignAdid);
        if ($collection->count() > 0) {
            $landingpage = '';
            foreach ($collection as $info) {
                $landingpage = $info->getData('landingpage');
            }
            return $landingpage;
        }
        return false;
    }

    public function getCampaignParamsArray($campaignAdid)
    {
        $utmData = [];
        $collection = $this->campaignCollection->create();
        $collection->addFieldToFilter('adid', $campaignAdid);
        if ($collection->count() > 0) {
            if (is_array($collection) || is_object($collection)) {
                foreach ($collection as $info) {
                    $utmData['utm_source'] = $info->getData('adid');
                    $utmData['utm_medium'] = $info->getData('type_label');
                    $utmData['utm_content'] = $info->getData('description');
                    $utmData['utm_campaign'] = $info->getData('campaignno') . '.' . $info->getData('adid');
                    $this->campaignSession->setData('campaign_no', $info->getData('campaignno'));
                }
            }
            return $utmData;
        }
        return false;
    }

    /* checkValidCampaign */
    public function checkIfValidCampaign($campaignAdid, $currentWebId = 1): bool
    {
        $collection = $this->campaignCollection->create();
        $collection->addFieldToFilter('adid', $campaignAdid)
            ->addFieldToFilter('website_id', $currentWebId)
            ->addFieldToSelect(['website_id', 'validitystartdate', 'validitystopdate']);

        if ($collection->count() > 0) {
            $todayDate = date('Y-m-d');
            $todayDate = date('Y-m-d', strtotime($todayDate));
            foreach ($collection as $item) {
                $startDate = '';
                $endDate = '';
                $startDate = date('Y-m-d', strtotime($item->getData('validitystartdate')));
                $endDate = date('Y-m-d', strtotime($item->getData('validitystopdate')));
//                 Extra Logs for testing
//                \Log::add('$todayDate : '.$todayDate,6);
//                \Log::add('$startDate : '.$startDate,6);
//                \Log::add('$endDate : '.$endDate,6);

                if (($todayDate >= $startDate) && ($todayDate <= $endDate)) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }

}
